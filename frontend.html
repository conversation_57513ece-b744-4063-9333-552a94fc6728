<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Epic免费游戏 - 精选游戏推荐</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="brand-icon">🎮</div>
                <span class="brand-text">Epic Free Games</span>
            </div>
            <div class="nav-actions">
                <button class="refresh-btn" id="refreshBtn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="23 4 23 10 17 10"></polyline>
                        <polyline points="1 20 1 14 7 14"></polyline>
                        <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                    </svg>
                    刷新
                </button>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 头部区域 -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">Epic 免费游戏精选</h1>
                <p class="hero-subtitle">只为您展示 Epic Games Store 的免费游戏，包括当前免费和即将免费的精彩内容</p>
                <div class="stats-container" id="statsContainer">
                    <div class="stat-item">
                        <span class="stat-number" id="totalGames">-</span>
                        <span class="stat-label">免费游戏总数</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="freeGames">-</span>
                        <span class="stat-label">当前免费</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="upcomingGames">-</span>
                        <span class="stat-label">即将免费</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 筛选器 -->
        <section class="filter-section">
            <div class="filter-container">
                <div class="filter-tabs">
                    <button class="filter-tab active" data-filter="all">全部免费</button>
                    <button class="filter-tab" data-filter="free">当前免费</button>
                    <button class="filter-tab" data-filter="upcoming">即将免费</button>
                    <button class="filter-tab" data-filter="base_game">免费基础游戏</button>
                </div>
            </div>
        </section>

        <!-- 游戏列表 -->
        <section class="games-section">
            <div class="games-container" id="gamesContainer">
                <!-- Loading 状态 -->
                <div class="loading-container" id="loadingContainer">
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                    </div>
                    <p class="loading-text">正在加载精彩游戏...</p>
                </div>
            </div>
        </section>

        <!-- 错误状态 -->
        <div class="error-container" id="errorContainer" style="display: none;">
            <div class="error-content">
                <div class="error-icon">⚠️</div>
                <h3 class="error-title">加载失败</h3>
                <p class="error-message" id="errorMessage">网络连接异常，请稍后重试</p>
                <button class="retry-btn" id="retryBtn">重新加载</button>
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-container" id="emptyContainer" style="display: none;">
            <div class="empty-content">
                <div class="empty-icon">🎮</div>
                <h3 class="empty-title">暂无免费游戏</h3>
                <p class="empty-message">当前筛选条件下没有找到免费游戏，请稍后再试或切换其他筛选条件</p>
            </div>
        </div>
    </main>

    <!-- 游戏详情模态框 -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal-container">
            <div class="modal-header">
                <button class="modal-close" id="modalClose">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-content" id="modalContent">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <!-- 回到顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="19" x2="12" y2="5"></line>
            <polyline points="5,12 12,5 19,12"></polyline>
        </svg>
    </button>

    <!-- Toast 通知 -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="script.js"></script>
</body>
</html>
