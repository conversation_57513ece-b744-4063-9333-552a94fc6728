// Epic免费游戏前端应用
class EpicGamesApp {
    constructor() {
        this.games = [];
        this.filteredGames = [];
        this.currentFilter = 'all';
        this.isLoading = false;
        this.apiUrl = 'webapi.php';
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadGames();
        this.initIntersectionObserver();
        this.initBackToTop();
    }
    
    bindEvents() {
        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadGames();
        });
        
        // 筛选器
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });
        
        // 模态框关闭
        document.getElementById('modalClose').addEventListener('click', () => {
            this.closeModal();
        });
        
        document.getElementById('modalOverlay').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closeModal();
            }
        });
        
        // 重试按钮
        document.getElementById('retryBtn').addEventListener('click', () => {
            this.loadGames();
        });
        
        // 回到顶部
        document.getElementById('backToTop').addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }
    
    async loadGames() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        this.setRefreshButtonLoading(true);
        
        try {
            const response = await fetch(this.apiUrl);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.games = data.games || [];
                this.updateStats(data);
                this.filterGames();
                this.showToast('success', '加载成功', `成功加载 ${this.games.length} 个游戏`);
            } else {
                throw new Error(data.message || '加载失败');
            }
            
        } catch (error) {
            console.error('加载游戏失败:', error);
            this.showError(error.message);
            this.showToast('error', '加载失败', error.message);
        } finally {
            this.isLoading = false;
            this.setRefreshButtonLoading(false);
        }
    }
    
    showLoading() {
        const container = document.getElementById('gamesContainer');
        const errorContainer = document.getElementById('errorContainer');
        const emptyContainer = document.getElementById('emptyContainer');
        
        container.innerHTML = `
            <div class="loading-container">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <p class="loading-text">正在加载精彩游戏...</p>
            </div>
        `;
        
        errorContainer.style.display = 'none';
        emptyContainer.style.display = 'none';
    }
    
    showError(message) {
        const container = document.getElementById('gamesContainer');
        const errorContainer = document.getElementById('errorContainer');
        const emptyContainer = document.getElementById('emptyContainer');
        
        container.innerHTML = '';
        document.getElementById('errorMessage').textContent = message;
        errorContainer.style.display = 'flex';
        emptyContainer.style.display = 'none';
    }
    
    showEmpty() {
        const container = document.getElementById('gamesContainer');
        const errorContainer = document.getElementById('errorContainer');
        const emptyContainer = document.getElementById('emptyContainer');
        
        container.innerHTML = '';
        errorContainer.style.display = 'none';
        emptyContainer.style.display = 'flex';
    }
    
    updateStats(data) {
        const totalGames = data.total_games || 0;
        const freeGames = this.games.filter(game => game.free_status?.is_currently_free).length;
        const upcomingGames = this.games.filter(game => game.free_status?.is_upcoming_free).length;
        
        this.animateNumber('totalGames', totalGames);
        this.animateNumber('freeGames', freeGames);
        this.animateNumber('upcomingGames', upcomingGames);
    }
    
    animateNumber(elementId, targetValue) {
        const element = document.getElementById(elementId);
        const startValue = parseInt(element.textContent) || 0;
        const duration = 1000;
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (targetValue - startValue) * this.easeOutCubic(progress));
            element.textContent = currentValue;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }
    
    setFilter(filter) {
        this.currentFilter = filter;
        
        // 更新筛选器UI
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
        
        this.filterGames();
    }
    
    filterGames() {
        switch (this.currentFilter) {
            case 'free':
                this.filteredGames = this.games.filter(game => game.free_status?.is_currently_free);
                break;
            case 'upcoming':
                this.filteredGames = this.games.filter(game => game.free_status?.is_upcoming_free);
                break;
            case 'base_game':
                this.filteredGames = this.games.filter(game => game.offer_type === 'BASE_GAME');
                break;
            default:
                this.filteredGames = [...this.games];
        }
        
        this.renderGames();
    }
    
    renderGames() {
        const container = document.getElementById('gamesContainer');
        const errorContainer = document.getElementById('errorContainer');
        const emptyContainer = document.getElementById('emptyContainer');
        
        errorContainer.style.display = 'none';
        
        if (this.filteredGames.length === 0) {
            this.showEmpty();
            return;
        }
        
        emptyContainer.style.display = 'none';
        
        container.innerHTML = this.filteredGames.map((game, index) => 
            this.createGameCard(game, index)
        ).join('');
        
        // 初始化懒加载图片
        this.initLazyLoading();
    }
    
    createGameCard(game, index) {
        const imageUrl = game.images?.OfferImageWide || game.images?.Thumbnail || '';
        const status = this.getGameStatus(game);
        const price = this.formatPrice(game.price);

        return `
            <div class="game-card" style="animation-delay: ${index * 0.1}s" onclick="app.showGameDetails('${game.id}')">
                <div class="game-image-container">
                    <img class="game-image lazy-image"
                         data-src="${imageUrl}"
                         alt="${game.title}"
                         loading="lazy">
                    <div class="game-status-badge badge-${status.class}">${status.text}</div>
                </div>
                <div class="game-content">
                    <h3 class="game-title">${game.title}</h3>
                    <p class="game-description">${game.description}</p>
                    <div class="game-meta">
                        <span class="game-seller">${game.seller?.name || '未知发行商'}</span>
                        <div class="game-price">
                            <span class="price-current">${price.current}</span>
                            ${price.original ? `<span class="price-original">${price.original}</span>` : ''}
                        </div>
                    </div>
                    <div class="game-actions">
                        <button class="btn-primary" onclick="event.stopPropagation(); app.openEpicStore('${game.id}')">
                            查看详情
                        </button>
                        <button class="btn-secondary" onclick="event.stopPropagation(); app.shareGame('${game.id}')">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="18" cy="5" r="3"></circle>
                                <circle cx="6" cy="12" r="3"></circle>
                                <circle cx="18" cy="19" r="3"></circle>
                                <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                                <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    getGameStatus(game) {
        if (game.free_status?.is_currently_free) {
            return { class: 'free', text: '免费' };
        } else if (game.free_status?.is_upcoming_free) {
            return { class: 'upcoming', text: '即将免费' };
        } else {
            return { class: 'paid', text: '付费' };
        }
    }

    formatPrice(price) {
        if (!price) return { current: '免费', original: null };

        const current = price.current === 0 ? '免费' : price.formatted?.current || '$0.00';
        const original = price.original > price.current ? price.formatted?.original : null;

        return { current, original };
    }

    initLazyLoading() {
        const images = document.querySelectorAll('.lazy-image');

        images.forEach(img => {
            if (img.dataset.src) {
                const imageLoader = new Image();
                imageLoader.onload = () => {
                    img.src = img.dataset.src;
                    img.classList.add('loaded');
                };
                imageLoader.onerror = () => {
                    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJIMTc2VjEwNEgxNDRWNzJaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xNTIgODBIMTY4Vjk2SDE1MlY4MFoiIGZpbGw9IiNGM0Y0RjYiLz4KPC9zdmc+';
                    img.classList.add('loaded');
                };
                imageLoader.src = img.dataset.src;
            }
        });
    }

    initIntersectionObserver() {
        const options = {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
        };

        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src && !img.src) {
                        this.loadImage(img);
                    }
                }
            });
        }, options);
    }

    loadImage(img) {
        const imageLoader = new Image();
        imageLoader.onload = () => {
            img.src = img.dataset.src;
            img.classList.add('loaded');
            this.observer.unobserve(img);
        };
        imageLoader.onerror = () => {
            img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJIMTc2VjEwNEgxNDRWNzJaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xNTIgODBIMTY4Vjk2SDE1MlY4MFoiIGZpbGw9IiNGM0Y0RjYiLz4KPC9zdmc+';
            img.classList.add('loaded');
            this.observer.unobserve(img);
        };
        imageLoader.src = img.dataset.src;
    }

    initBackToTop() {
        const backToTopBtn = document.getElementById('backToTop');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });
    }

    setRefreshButtonLoading(loading) {
        const btn = document.getElementById('refreshBtn');
        if (loading) {
            btn.classList.add('loading');
            btn.disabled = true;
        } else {
            btn.classList.remove('loading');
            btn.disabled = false;
        }
    }

    showGameDetails(gameId) {
        const game = this.games.find(g => g.id === gameId);
        if (!game) return;

        const modal = document.getElementById('modalOverlay');
        const content = document.getElementById('modalContent');

        content.innerHTML = this.createGameDetailsHTML(game);
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    createGameDetailsHTML(game) {
        const status = this.getGameStatus(game);
        const price = this.formatPrice(game.price);
        const imageUrl = game.images?.OfferImageWide || game.images?.Thumbnail || '';

        return `
            <div class="game-details">
                <div class="game-details-image">
                    <img src="${imageUrl}" alt="${game.title}" style="width: 100%; border-radius: 12px;">
                </div>
                <div class="game-details-content">
                    <div class="game-status-badge badge-${status.class}" style="display: inline-block; margin-bottom: 16px;">
                        ${status.text}
                    </div>
                    <h2 style="font-size: 24px; font-weight: 700; margin-bottom: 8px; color: var(--text-primary);">
                        ${game.title}
                    </h2>
                    <p style="color: var(--text-secondary); margin-bottom: 16px; font-size: 14px;">
                        发行商: ${game.seller?.name || '未知'}
                    </p>
                    <p style="color: var(--text-secondary); line-height: 1.6; margin-bottom: 24px;">
                        ${game.description}
                    </p>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                        <div class="game-price">
                            <span class="price-current" style="font-size: 20px;">${price.current}</span>
                            ${price.original ? `<span class="price-original" style="margin-left: 8px;">${price.original}</span>` : ''}
                        </div>
                    </div>
                    <div style="display: flex; gap: 12px;">
                        <button class="btn-primary" onclick="app.openEpicStore('${game.id}')" style="flex: 1;">
                            在Epic商店中查看
                        </button>
                        <button class="btn-secondary" onclick="app.shareGame('${game.id}')">
                            分享
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    closeModal() {
        const modal = document.getElementById('modalOverlay');
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }

    openEpicStore(gameId) {
        const game = this.games.find(g => g.id === gameId);
        if (!game) {
            this.showToast('warning', '提示', '游戏信息不可用');
            return;
        }

        let epicUrl = '';

        // 优先使用 productSlug
        if (game.product_slug) {
            epicUrl = `https://store.epicgames.com/zh-CN/p/${game.product_slug}`;
        }
        // 如果没有 productSlug，尝试使用 urlSlug
        else if (game.url_slug) {
            epicUrl = `https://store.epicgames.com/zh-CN/p/${game.url_slug}`;
        }
        // 如果都没有，使用搜索方式
        else if (game.title) {
            epicUrl = `https://store.epicgames.com/zh-CN/browse?q=${encodeURIComponent(game.title)}`;
        }
        else {
            this.showToast('warning', '提示', '游戏链接不可用');
            return;
        }

        window.open(epicUrl, '_blank');
        this.showToast('success', '跳转成功', '已在新标签页中打开Epic商店');
    }

    shareGame(gameId) {
        const game = this.games.find(g => g.id === gameId);
        if (!game) return;

        const shareData = {
            title: `${game.title} - Epic免费游戏`,
            text: `发现了一个不错的游戏：${game.title}`,
            url: window.location.href
        };

        if (navigator.share) {
            navigator.share(shareData).then(() => {
                this.showToast('success', '分享成功', '游戏信息已分享');
            }).catch((error) => {
                console.log('分享失败:', error);
                this.fallbackShare(game);
            });
        } else {
            this.fallbackShare(game);
        }
    }

    fallbackShare(game) {
        const shareText = `${game.title} - Epic免费游戏\n${game.description}\n${window.location.href}`;

        if (navigator.clipboard) {
            navigator.clipboard.writeText(shareText).then(() => {
                this.showToast('success', '复制成功', '游戏信息已复制到剪贴板');
            }).catch(() => {
                this.showToast('error', '复制失败', '请手动复制游戏信息');
            });
        } else {
            // 创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = shareText;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                this.showToast('success', '复制成功', '游戏信息已复制到剪贴板');
            } catch (err) {
                this.showToast('error', '复制失败', '请手动复制游戏信息');
            }

            document.body.removeChild(textArea);
        }
    }

    showToast(type, title, message) {
        const container = document.getElementById('toastContainer');
        const toastId = 'toast_' + Date.now();

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.id = toastId;
        toast.innerHTML = `
            <div class="toast-title">${title}</div>
            <div class="toast-message">${message}</div>
        `;

        container.appendChild(toast);

        // 触发显示动画
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // 自动移除
        setTimeout(() => {
            this.removeToast(toastId);
        }, 4000);
    }

    removeToast(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }
}

// 初始化应用
const app = new EpicGamesApp();
