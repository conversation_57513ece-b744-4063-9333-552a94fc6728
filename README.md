# Epic免费游戏资讯API

这是一个用于获取Epic Games商店免费游戏信息的PHP API，提供两个版本：原始数据版本和前端显示版本。

## API文件说明

### 1. epic_raw_api.php - 原始数据API
直接返回Epic商店接口的原始JSON数据，适合需要完整数据的场景。

**特点：**
- 返回Epic官方接口的完整原始数据
- 数据结构与Epic官方接口保持一致
- 适合需要所有字段信息的开发者使用

**使用方法：**
```
GET /epic_raw_api.php
```

### 2. epic_frontend_api.php - 前端显示API
对Epic商店数据进行格式化处理，提取关键信息，适合前端显示使用。

**特点：**
- 数据结构简化，便于前端使用
- 提取游戏的关键信息（标题、描述、图片、价格等）
- 包含免费状态判断
- 格式化价格和促销信息
- 添加时间戳和统计信息

**使用方法：**
```
GET /epic_frontend_api.php
```

## 返回数据格式

### 原始数据API返回格式
返回Epic官方接口的完整JSON数据结构。

### 前端显示API返回格式
```json
{
  "success": true,
  "timestamp": "2025-07-26 12:00:00",
  "total_games": 7,
  "games": [
    {
      "id": "游戏ID",
      "title": "游戏标题",
      "description": "游戏描述",
      "namespace": "命名空间",
      "offer_type": "优惠类型",
      "status": "状态",
      "effective_date": "生效日期",
      "expiry_date": "过期日期",
      "viewable_date": "可见日期",
      "url_slug": "URL标识",
      "product_slug": "产品标识",
      "seller": {
        "id": "发行商ID",
        "name": "发行商名称"
      },
      "images": {
        "OfferImageWide": "宽屏图片URL",
        "OfferImageTall": "竖屏图片URL",
        "Thumbnail": "缩略图URL"
      },
      "price": {
        "original": 1999,
        "discount": 0,
        "current": 0,
        "currency": "USD",
        "formatted": {
          "original": "$19.99",
          "current": "$0.00"
        }
      },
      "promotions": {
        "current": [
          {
            "start_date": "开始日期",
            "end_date": "结束日期",
            "discount_type": "折扣类型",
            "discount_percentage": 0
          }
        ],
        "upcoming": []
      },
      "free_status": {
        "is_free_game": true,
        "is_currently_free": true,
        "is_upcoming_free": false
      }
    }
  ]
}
```

## 错误处理

当API请求失败时，会返回错误信息：

```json
{
  "success": false,
  "error": "错误详情",
  "message": "错误描述",
  "timestamp": "2025-07-26 12:00:00"
}
```

## 部署说明

1. 将PHP文件上传到支持PHP的Web服务器
2. 确保服务器支持cURL扩展
3. 确保服务器可以访问外部网络（访问Epic API）
4. 通过浏览器或HTTP客户端访问对应的PHP文件

## 注意事项

- API数据来源于Epic Games官方接口，数据更新频率取决于Epic官方
- 建议在生产环境中添加缓存机制，避免频繁请求Epic接口
- 请遵守Epic Games的使用条款和API使用规范
- 建议添加适当的错误日志记录功能

## 技术特性

- 支持CORS跨域请求
- 使用UTF-8编码，支持中文显示
- 包含完整的错误处理机制
- 使用cURL进行HTTP请求，支持SSL
- JSON格式化输出，便于调试

## 免费游戏判断逻辑

前端显示API包含智能的免费游戏判断：

1. **is_free_game**: 检查游戏是否在"freegames"分类中
2. **is_currently_free**: 检查当前是否有100%折扣促销且价格为0
3. **is_upcoming_free**: 检查是否有即将到来的免费促销

这样可以准确识别当前免费、即将免费和曾经免费的游戏。
