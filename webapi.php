<?php
/**
 * Epic免费游戏资讯API - 前端显示版本
 * 格式化Epic商场数据，适合前端显示使用
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Epic商场免费游戏接口URL - 中文版
$epic_api_url = 'https://store-site-backend-static-ipv4.ak.epicgames.com/freeGamesPromotions?locale=zh-CN&country=CN&allowCountries=CN';

/**
 * 获取Epic免费游戏数据
 */
function getEpicFreeGames() {
    global $epic_api_url;
    
    // 初始化cURL
    $ch = curl_init();
    
    // 设置cURL选项
    curl_setopt_array($ch, [
        CURLOPT_URL => $epic_api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
        ]
    ]);
    
    // 执行请求
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    // 检查请求是否成功
    if ($response === false || !empty($error)) {
        return [
            'success' => false,
            'error' => 'cURL错误: ' . $error,
            'data' => null
        ];
    }
    
    if ($http_code !== 200) {
        return [
            'success' => false,
            'error' => 'HTTP错误: ' . $http_code,
            'data' => null
        ];
    }
    
    // 解析JSON
    $data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        return [
            'success' => false,
            'error' => 'JSON解析错误: ' . json_last_error_msg(),
            'data' => null
        ];
    }
    
    return [
        'success' => true,
        'error' => null,
        'data' => $data
    ];
}

/**
 * 格式化游戏数据，适合前端显示
 */
function formatGamesForFrontend($rawData) {
    if (!isset($rawData['data']['Catalog']['searchStore']['elements'])) {
        return [];
    }
    
    $games = $rawData['data']['Catalog']['searchStore']['elements'];
    $formattedGames = [];
    
    foreach ($games as $game) {
        // 获取图片
        $images = [];
        if (isset($game['keyImages']) && is_array($game['keyImages'])) {
            foreach ($game['keyImages'] as $image) {
                $images[$image['type']] = $image['url'];
            }
        }
        
        // 获取价格信息
        $price = [
            'original' => 0,
            'discount' => 0,
            'current' => 0,
            'currency' => 'USD',
            'formatted' => [
                'original' => '$0.00',
                'current' => '$0.00'
            ]
        ];
        
        if (isset($game['price']['totalPrice'])) {
            $priceData = $game['price']['totalPrice'];
            $price = [
                'original' => $priceData['originalPrice'] ?? 0,
                'discount' => $priceData['discountPrice'] ?? 0,
                'current' => $priceData['discountPrice'] ?? 0,
                'currency' => $priceData['currencyCode'] ?? 'USD',
                'formatted' => [
                    'original' => $priceData['fmtPrice']['originalPrice'] ?? '$0.00',
                    'current' => $priceData['fmtPrice']['discountPrice'] ?? '$0.00'
                ]
            ];
        }
        
        // 获取促销信息
        $promotions = [
            'current' => [],
            'upcoming' => []
        ];
        
        if (isset($game['promotions'])) {
            // 当前促销
            if (isset($game['promotions']['promotionalOffers']) && is_array($game['promotions']['promotionalOffers'])) {
                foreach ($game['promotions']['promotionalOffers'] as $promoGroup) {
                    if (isset($promoGroup['promotionalOffers']) && is_array($promoGroup['promotionalOffers'])) {
                        foreach ($promoGroup['promotionalOffers'] as $promo) {
                            $promotions['current'][] = [
                                'start_date' => $promo['startDate'] ?? null,
                                'end_date' => $promo['endDate'] ?? null,
                                'discount_type' => $promo['discountSetting']['discountType'] ?? null,
                                'discount_percentage' => $promo['discountSetting']['discountPercentage'] ?? 0
                            ];
                        }
                    }
                }
            }
            
            // 即将到来的促销
            if (isset($game['promotions']['upcomingPromotionalOffers']) && is_array($game['promotions']['upcomingPromotionalOffers'])) {
                foreach ($game['promotions']['upcomingPromotionalOffers'] as $promoGroup) {
                    if (isset($promoGroup['promotionalOffers']) && is_array($promoGroup['promotionalOffers'])) {
                        foreach ($promoGroup['promotionalOffers'] as $promo) {
                            $promotions['upcoming'][] = [
                                'start_date' => $promo['startDate'] ?? null,
                                'end_date' => $promo['endDate'] ?? null,
                                'discount_type' => $promo['discountSetting']['discountType'] ?? null,
                                'discount_percentage' => $promo['discountSetting']['discountPercentage'] ?? 0
                            ];
                        }
                    }
                }
            }
        }
        
        // 判断是否为免费游戏
        $isFree = false;
        $isCurrentlyFree = false;
        $isUpcomingFree = false;
        
        // 检查是否在freegames分类中
        if (isset($game['categories']) && is_array($game['categories'])) {
            foreach ($game['categories'] as $category) {
                if (isset($category['path']) && $category['path'] === 'freegames') {
                    $isFree = true;
                    break;
                }
            }
        }
        
        // 检查当前是否免费
        if (!empty($promotions['current'])) {
            foreach ($promotions['current'] as $promo) {
                // 100%折扣或者折扣后价格为0都认为是免费
                if ($promo['discount_percentage'] === 100 ||
                    ($promo['discount_percentage'] === 0 && $price['current'] === 0)) {
                    $isCurrentlyFree = true;
                    break;
                }
            }
        }

        // 如果价格为0，也认为是免费的
        if ($price['current'] === 0) {
            $isCurrentlyFree = true;
        }

        // 如果在freegames分类中，通常也是当前免费的
        if ($isFree && $price['current'] === 0) {
            $isCurrentlyFree = true;
        }

        // 检查即将免费
        if (!empty($promotions['upcoming'])) {
            foreach ($promotions['upcoming'] as $promo) {
                // 即将到来的100%折扣
                if ($promo['discount_percentage'] === 100) {
                    $isUpcomingFree = true;
                    break;
                }
            }
        }
        
        // 获取正确的Epic商店页面slug
        $pageSlug = null;

        // 优先从 catalogNs.mappings 获取
        if (isset($game['catalogNs']['mappings']) && is_array($game['catalogNs']['mappings'])) {
            foreach ($game['catalogNs']['mappings'] as $mapping) {
                if (isset($mapping['pageSlug']) && $mapping['pageType'] === 'productHome') {
                    $pageSlug = $mapping['pageSlug'];
                    break;
                }
            }
        }

        // 如果没有找到，尝试从 offerMappings 获取
        if (!$pageSlug && isset($game['offerMappings']) && is_array($game['offerMappings'])) {
            foreach ($game['offerMappings'] as $mapping) {
                if (isset($mapping['pageSlug']) && $mapping['pageType'] === 'productHome') {
                    $pageSlug = $mapping['pageSlug'];
                    break;
                }
            }
        }

        // 如果还没有找到，使用 productSlug
        if (!$pageSlug) {
            $pageSlug = $game['productSlug'] ?? null;
        }

        // 格式化游戏数据
        $formattedGame = [
            'id' => $game['id'] ?? '',
            'title' => $game['title'] ?? '',
            'description' => $game['description'] ?? '',
            'namespace' => $game['namespace'] ?? '',
            'offer_type' => $game['offerType'] ?? '',
            'status' => $game['status'] ?? '',
            'effective_date' => $game['effectiveDate'] ?? null,
            'expiry_date' => $game['expiryDate'] ?? null,
            'viewable_date' => $game['viewableDate'] ?? null,
            'url_slug' => $game['urlSlug'] ?? '',
            'product_slug' => $game['productSlug'] ?? null,
            'page_slug' => $pageSlug,  // 新增：正确的Epic商店页面slug
            'seller' => [
                'id' => $game['seller']['id'] ?? '',
                'name' => $game['seller']['name'] ?? ''
            ],
            'images' => $images,
            'price' => $price,
            'promotions' => $promotions,
            'free_status' => [
                'is_free_game' => $isFree,
                'is_currently_free' => $isCurrentlyFree,
                'is_upcoming_free' => $isUpcomingFree
            ]
        ];
        
        $formattedGames[] = $formattedGame;
    }
    
    return $formattedGames;
}

// 处理请求
try {
    $result = getEpicFreeGames();
    
    if ($result['success']) {
        $formattedGames = formatGamesForFrontend($result['data']);
        
        // 返回格式化的数据
        echo json_encode([
            'success' => true,
            'timestamp' => date('Y-m-d H:i:s'),
            'total_games' => count($formattedGames),
            'games' => $formattedGames
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        
    } else {
        // 返回错误信息
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $result['error'],
            'message' => '获取Epic免费游戏数据失败',
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'message' => '服务器内部错误',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
