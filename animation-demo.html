<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动画效果演示 - Epic免费游戏</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .demo-section {
            padding: 2rem;
            margin: 2rem 0;
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
        }
        
        .demo-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }
        
        .demo-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }
        
        .demo-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <h1 style="text-align: center; margin: 2rem 0; font-size: 2.5rem; color: var(--text-primary);">
            动画效果演示
        </h1>
        
        <!-- Toast 通知演示 -->
        <div class="demo-section">
            <h2 class="demo-title">Toast 通知动画</h2>
            <div class="demo-buttons">
                <button class="btn-primary" onclick="showDemoToast('success', '成功', '操作已成功完成！')">
                    成功通知
                </button>
                <button class="btn-primary" onclick="showDemoToast('error', '错误', '发生了一个错误，请重试。')">
                    错误通知
                </button>
                <button class="btn-primary" onclick="showDemoToast('warning', '警告', '请注意这个重要信息。')">
                    警告通知
                </button>
                <button class="btn-primary" onclick="showDemoToast('info', '信息', '这是一条普通信息。')">
                    信息通知
                </button>
            </div>
        </div>
        
        <!-- 按钮动画演示 -->
        <div class="demo-section">
            <h2 class="demo-title">按钮动画效果</h2>
            <div class="demo-buttons">
                <button class="btn-primary">主要按钮</button>
                <button class="btn-secondary">次要按钮</button>
                <button class="refresh-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="23 4 23 10 17 10"></polyline>
                        <polyline points="1 20 1 14 7 14"></polyline>
                        <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                    </svg>
                    刷新按钮
                </button>
            </div>
        </div>
        
        <!-- 筛选器动画演示 -->
        <div class="demo-section">
            <h2 class="demo-title">筛选器动画</h2>
            <div class="filter-container">
                <div class="filter-tabs">
                    <button class="filter-tab active">全部游戏</button>
                    <button class="filter-tab">当前免费</button>
                    <button class="filter-tab">即将免费</button>
                    <button class="filter-tab">基础游戏</button>
                </div>
            </div>
        </div>
        
        <!-- 加载动画演示 -->
        <div class="demo-section">
            <h2 class="demo-title">加载动画</h2>
            <div class="loading-container">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <p class="loading-text">正在加载精彩游戏...</p>
            </div>
        </div>
        
        <!-- 游戏卡片演示 -->
        <div class="demo-section">
            <h2 class="demo-title">游戏卡片动画</h2>
            <button class="btn-primary" onclick="showDemoCards()" style="margin-bottom: 2rem;">
                显示卡片动画
            </button>
            <div class="demo-cards" id="demoCards">
                <!-- 卡片将通过JavaScript动态添加 -->
            </div>
        </div>
    </div>
    
    <!-- Toast 容器 -->
    <div class="toast-container" id="toastContainer"></div>
    
    <!-- 回到顶部按钮 -->
    <button class="back-to-top visible">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="19" x2="12" y2="5"></line>
            <polyline points="5,12 12,5 19,12"></polyline>
        </svg>
    </button>
    
    <script>
        // 简化的Toast功能
        function showDemoToast(type, title, message) {
            const container = document.getElementById('toastContainer');
            const toastId = 'toast_' + Date.now();
            
            const icons = {
                success: '✓',
                error: '✕',
                warning: '⚠',
                info: 'ℹ'
            };
            
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.id = toastId;
            toast.innerHTML = `
                <div class="toast-content">
                    <div class="toast-icon">${icons[type]}</div>
                    <div class="toast-text">
                        <div class="toast-title">${title}</div>
                        <div class="toast-message">${message}</div>
                    </div>
                    <button class="toast-close" onclick="removeDemoToast('${toastId}')">×</button>
                </div>
                <div class="toast-progress"></div>
            `;
            
            container.appendChild(toast);
            
            requestAnimationFrame(() => {
                toast.classList.add('show');
            });
            
            const progressBar = toast.querySelector('.toast-progress');
            if (progressBar) {
                progressBar.style.animation = 'toastProgress 4000ms linear';
            }
            
            setTimeout(() => {
                removeDemoToast(toastId);
            }, 4000);
        }
        
        function removeDemoToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.classList.add('hide');
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }
        
        // 演示卡片动画
        function showDemoCards() {
            const container = document.getElementById('demoCards');
            const demoGames = [
                { title: '示例游戏 1', description: '这是一个精彩的游戏描述...', status: 'free' },
                { title: '示例游戏 2', description: '另一个有趣的游戏...', status: 'upcoming' },
                { title: '示例游戏 3', description: '更多精彩内容等你发现...', status: 'paid' }
            ];
            
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                container.innerHTML = demoGames.map((game, index) => `
                    <div class="game-card" style="opacity: 0; transform: translateY(30px) scale(0.95);">
                        <div class="game-image-container">
                            <div style="width: 100%; height: 200px; background: linear-gradient(45deg, #007AFF, #5856D6); border-radius: 8px;"></div>
                            <div class="game-status-badge badge-${game.status}">${game.status === 'free' ? '免费' : game.status === 'upcoming' ? '即将免费' : '付费'}</div>
                        </div>
                        <div class="game-content">
                            <h3 class="game-title">${game.title}</h3>
                            <p class="game-description">${game.description}</p>
                            <div class="game-actions">
                                <button class="btn-primary">查看详情</button>
                                <button class="btn-secondary">分享</button>
                            </div>
                        </div>
                    </div>
                `).join('');
                
                container.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
                
                // 交错显示卡片
                const cards = container.querySelectorAll('.game-card');
                cards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.transition = 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0) scale(1)';
                    }, index * 200);
                });
            }, 150);
        }
        
        // 筛选器切换演示
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
            });
        });
        
        // 回到顶部功能
        document.querySelector('.back-to-top').addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    </script>
</body>
</html>
