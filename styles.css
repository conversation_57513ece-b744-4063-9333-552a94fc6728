/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 苹果风格色彩系统 */
    --primary-color: #007AFF;
    --secondary-color: #5856D6;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --error-color: #FF3B30;
    
    /* 中性色 */
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;
    
    /* 背景色 */
    --bg-primary: #FFFFFF;
    --bg-secondary: #F8F9FA;
    --bg-tertiary: #F1F3F4;
    
    /* 文字色 */
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --text-tertiary: #A1A1A6;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 圆角 */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* 动画 */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-spring: 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #000000;
        --bg-secondary: #1C1C1E;
        --bg-tertiary: #2C2C2E;
        --text-primary: #FFFFFF;
        --text-secondary: #EBEBF5;
        --text-tertiary: #EBEBF5;
    }
}

/* 基础样式 */
html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 16px;
    overflow-x: hidden;
}

/* 导航栏 */
.navbar {
    position: sticky;
    top: 0;
    z-index: 100;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-md) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.brand-icon {
    font-size: 24px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.brand-text {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.refresh-btn:hover {
    background: #0056CC;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.refresh-btn:active {
    transform: translateY(0);
}

.refresh-btn svg {
    transition: transform var(--transition-normal);
}

.refresh-btn.loading svg {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 主要内容区域 */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* 头部区域 */
.hero-section {
    text-align: center;
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(135deg, var(--primary-color)10, var(--secondary-color)10);
    border-radius: var(--radius-xl);
    margin: var(--spacing-xl) 0;
}

.hero-title {
    font-size: clamp(32px, 5vw, 48px);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    letter-spacing: -0.02em;
}

.hero-subtitle {
    font-size: 18px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.stats-container {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* 筛选器 */
.filter-section {
    margin: var(--spacing-xl) 0;
}

.filter-container {
    display: flex;
    justify-content: center;
}

.filter-tabs {
    display: flex;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    box-shadow: var(--shadow-sm);
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.filter-tab {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.filter-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease-out;
}

.filter-tab:hover {
    background: var(--gray-100);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.filter-tab:hover::before {
    left: 100%;
}

.filter-tab.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-md);
    transform: translateY(-2px) scale(1.05);
}

.filter-tab.active::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

/* 游戏列表 */
.games-section {
    margin-bottom: var(--spacing-2xl);
}

.games-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

/* Loading 状态 */
.loading-container {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    gap: var(--spacing-lg);
    animation: fadeIn 0.5s ease-out;
}

.loading-spinner {
    position: relative;
}

.spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
}

.loading-text {
    color: var(--text-secondary);
    font-size: 16px;
    font-weight: 500;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.6;
    }
}

/* 游戏卡片 */
.game-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    cursor: pointer;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    animation: fadeInUp 0.6s var(--transition-spring) forwards;
    will-change: transform, opacity, box-shadow;
}

.game-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.game-card:active {
    transform: translateY(-4px) scale(1.01);
    transition: all 0.1s ease-out;
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    60% {
        opacity: 0.8;
        transform: translateY(-5px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.game-image-container {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
    background: var(--gray-100);
}

.game-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow), filter var(--transition-normal);
    will-change: transform, filter;
}

.game-card:hover .game-image {
    transform: scale(1.08);
    filter: brightness(1.1) contrast(1.05);
}

.game-status-badge {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transform: translateZ(0);
}

.game-card:hover .game-status-badge {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.badge-free {
    background: var(--success-color);
    color: white;
}

.badge-upcoming {
    background: var(--warning-color);
    color: white;
}

.badge-paid {
    background: var(--gray-600);
    color: white;
}

.game-content {
    padding: var(--spacing-lg);
}

.game-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.game-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.game-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.game-seller {
    font-size: 12px;
    color: var(--text-tertiary);
    font-weight: 500;
}

.game-price {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.price-current {
    font-size: 16px;
    font-weight: 700;
    color: var(--success-color);
}

.price-original {
    font-size: 14px;
    color: var(--text-tertiary);
    text-decoration: line-through;
}

.game-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-primary {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease-out;
}

.btn-primary:hover {
    background: #0056CC;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:active {
    transform: translateY(0) scale(0.98);
    transition: all 0.1s ease-out;
}

.btn-secondary {
    padding: var(--spacing-sm);
    background: var(--gray-100);
    color: var(--text-secondary);
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.btn-secondary:hover {
    background: var(--gray-200);
    color: var(--text-primary);
    transform: translateY(-1px) scale(1.05);
}

.btn-secondary:active {
    transform: translateY(0) scale(0.95);
    transition: all 0.1s ease-out;
}

/* 错误和空状态 */
.error-container,
.empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    text-align: center;
}

.error-content,
.empty-content {
    max-width: 400px;
    padding: var(--spacing-xl);
}

.error-icon,
.empty-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-lg);
}

.error-title,
.empty-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.error-message,
.empty-message {
    font-size: 16px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.5;
}

.retry-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.retry-btn:hover {
    background: #0056CC;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--spacing-md);
    }

    .main-content {
        padding: 0 var(--spacing-md);
    }

    .hero-section {
        padding: var(--spacing-xl) var(--spacing-md);
        margin: var(--spacing-lg) 0;
    }

    .stats-container {
        gap: var(--spacing-lg);
    }

    .games-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .filter-tabs {
        justify-content: center;
    }

    .game-content {
        padding: var(--spacing-md);
    }
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    max-width: 600px;
    max-height: 80vh;
    width: 90%;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    transform: scale(0.8) translateY(40px) rotateX(10deg);
    transition: all var(--transition-spring);
    perspective: 1000px;
}

.modal-overlay.active .modal-container {
    transform: scale(1) translateY(0) rotateX(0deg);
}

.modal-header {
    display: flex;
    justify-content: flex-end;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.modal-close {
    background: var(--gray-100);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-200);
    transform: scale(1.1);
}

.modal-content {
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: calc(80vh - 80px);
}

/* Toast 通知 */
.toast-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-color);
    min-width: 300px;
    max-width: 400px;
    transform: translateX(120%) scale(0.9);
    transition: all var(--transition-bounce);
    opacity: 0;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.toast::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease-out;
}

.toast.show {
    transform: translateX(0) scale(1);
    opacity: 1;
}

.toast.show::before {
    left: 100%;
}

.toast.hide {
    transform: translateX(120%) scale(0.8);
    opacity: 0;
    transition: all var(--transition-fast);
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--error-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    position: relative;
}

.toast-icon {
    font-size: 16px;
    font-weight: bold;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 2px;
}

.toast.success .toast-icon {
    background: var(--success-color);
    color: white;
}

.toast.error .toast-icon {
    background: var(--error-color);
    color: white;
}

.toast.warning .toast-icon {
    background: var(--warning-color);
    color: white;
}

.toast.info .toast-icon {
    background: var(--primary-color);
    color: white;
}

.toast-text {
    flex: 1;
}

.toast-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    position: relative;
}

.toast-icon {
    font-size: 16px;
    font-weight: bold;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 2px;
}

.toast.success .toast-icon {
    background: var(--success-color);
    color: white;
}

.toast.error .toast-icon {
    background: var(--error-color);
    color: white;
}

.toast.warning .toast-icon {
    background: var(--warning-color);
    color: white;
}

.toast.info .toast-icon {
    background: var(--primary-color);
    color: white;
}

.toast-text {
    flex: 1;
}

.toast-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.toast-message {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.toast-close:hover {
    background: var(--gray-100);
    color: var(--text-primary);
    transform: scale(1.1);
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--primary-color);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    transform-origin: left;
    width: 100%;
}

.toast.success .toast-progress {
    background: var(--success-color);
}

.toast.error .toast-progress {
    background: var(--error-color);
}

.toast.warning .toast-progress {
    background: var(--warning-color);
}

@keyframes toastProgress {
    from {
        transform: scaleX(1);
    }
    to {
        transform: scaleX(0);
    }
}

.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.toast-close:hover {
    background: var(--gray-100);
    color: var(--text-primary);
    transform: scale(1.1);
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--primary-color);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    transform-origin: left;
    width: 100%;
}

.toast.success .toast-progress {
    background: var(--success-color);
}

.toast.error .toast-progress {
    background: var(--error-color);
}

.toast.warning .toast-progress {
    background: var(--warning-color);
}

@keyframes toastProgress {
    from {
        transform: scaleX(1);
    }
    to {
        transform: scaleX(0);
    }
}

/* 回到顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 56px;
    height: 56px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transform: translateY(100px) scale(0.8);
    opacity: 0;
    transition: all var(--transition-bounce);
    z-index: 999;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.back-to-top::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: var(--primary-color);
    transform: scale(0);
    transition: transform 0.3s ease-out;
}

.back-to-top.visible {
    transform: translateY(0) scale(1);
    opacity: 1;
}

.back-to-top:hover {
    background: #0056CC;
    transform: translateY(-4px) scale(1.1);
    box-shadow: var(--shadow-xl);
}

.back-to-top:hover::before {
    transform: scale(1.2);
    opacity: 0.3;
}

.back-to-top:active {
    transform: translateY(-2px) scale(1.05);
    transition: all 0.1s ease-out;
}

/* 懒加载图片 */
.lazy-image {
    opacity: 0;
    transition: opacity var(--transition-slow);
}

.lazy-image.loaded {
    opacity: 1;
}

/* 骨架屏动画 */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.skeleton-image {
    width: 100%;
    height: 200px;
    border-radius: var(--radius-sm);
}

.skeleton-text {
    height: 16px;
    border-radius: var(--radius-xs);
    margin-bottom: var(--spacing-sm);
}

.skeleton-text.title {
    height: 20px;
    width: 80%;
}

.skeleton-text.description {
    width: 100%;
}

.skeleton-text.short {
    width: 60%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 选择文本样式 */
::selection {
    background: var(--primary-color);
    color: white;
}

/* 焦点样式 */
button:focus,
input:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 禁用状态 */
.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}
