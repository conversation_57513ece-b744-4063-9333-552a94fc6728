<?php
/**
 * Epic免费游戏资讯API - 原始数据版本
 * 直接返回Epic商场接口的原始JSON数据
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Epic商场免费游戏接口URL - 中文版
$epic_api_url = 'https://store-site-backend-static-ipv4.ak.epicgames.com/freeGamesPromotions?locale=zh-CN&country=CN&allowCountries=CN';

/**
 * 获取Epic免费游戏数据
 */
function getEpicFreeGames() {
    global $epic_api_url;
    
    // 初始化cURL
    $ch = curl_init();
    
    // 设置cURL选项
    curl_setopt_array($ch, [
        CURLOPT_URL => $epic_api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
        ]
    ]);
    
    // 执行请求
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    // 检查请求是否成功
    if ($response === false || !empty($error)) {
        return [
            'success' => false,
            'error' => 'cURL错误: ' . $error,
            'data' => null
        ];
    }
    
    if ($http_code !== 200) {
        return [
            'success' => false,
            'error' => 'HTTP错误: ' . $http_code,
            'data' => null
        ];
    }
    
    // 解析JSON
    $data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        return [
            'success' => false,
            'error' => 'JSON解析错误: ' . json_last_error_msg(),
            'data' => null
        ];
    }
    
    return [
        'success' => true,
        'error' => null,
        'data' => $data
    ];
}

// 处理请求
try {
    $result = getEpicFreeGames();
    
    if ($result['success']) {
        // 直接返回Epic API的原始数据
        echo json_encode($result['data'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    } else {
        // 返回错误信息
        http_response_code(500);
        echo json_encode([
            'error' => $result['error'],
            'message' => '获取Epic免费游戏数据失败'
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'message' => '服务器内部错误'
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
